"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/FirstLandingDash */ \"(app-pages-browser)/./src/components/dashboard/FirstLandingDash.tsx\");\n/* harmony import */ var _components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/TopTrendsTable */ \"(app-pages-browser)/./src/components/dashboard/TopTrendsTable.tsx\");\n/* harmony import */ var _components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/NewListedCoins */ \"(app-pages-browser)/./src/components/dashboard/NewListedCoins.tsx\");\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(app-pages-browser)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var _components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/MainDash */ \"(app-pages-browser)/./src/components/dashboard/MainDash.tsx\");\n/* harmony import */ var _components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/AccountTabContent */ \"(app-pages-browser)/./src/components/dashboard/AccountTabContent.tsx\");\n/* harmony import */ var _components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/TransactionsTabContent */ \"(app-pages-browser)/./src/components/dashboard/TransactionsTabContent.tsx\");\n/* harmony import */ var _data_dashboard_data__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/data/dashboard-data */ \"(app-pages-browser)/./src/data/dashboard-data.ts\");\n/* harmony import */ var _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/data/loanDashboardMockData */ \"(app-pages-browser)/./src/data/loanDashboardMockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardHomePage() {\n    _s();\n    const { isMobile, isTablet, isDesktop } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showMainDash, setShowMainDash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forceToggle, setForceToggle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch dashboard data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardHomePage.useEffect\": ()=>{\n            const loadDashboardData = {\n                \"DashboardHomePage.useEffect.loadDashboardData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_data_dashboard_data__WEBPACK_IMPORTED_MODULE_13__.fetchDashboardData)();\n                        setDashboardData(data);\n                        setShowMainDash(data.hasUserData);\n                    } catch (error) {\n                        console.error('Failed to load dashboard data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardHomePage.useEffect.loadDashboardData\"];\n            loadDashboardData();\n        }\n    }[\"DashboardHomePage.useEffect\"], []);\n    // Handle toggle between dashboards\n    const handleToggleDashboard = ()=>{\n        setForceToggle(!forceToggle);\n        setShowMainDash(!showMainDash);\n    };\n    var _dashboardData_hasUserData;\n    // Determine which dashboard to show\n    const shouldShowMainDash = forceToggle ? showMainDash : (_dashboardData_hasUserData = dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.hasUserData) !== null && _dashboardData_hasUserData !== void 0 ? _dashboardData_hasUserData : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm sm:text-base\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    if (!dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm sm:text-base\",\n                    children: \"Failed to load dashboard data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // Mobile and Tablet Layout (< 1200px)\n    if (isMobile || isTablet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-2 sm:p-4 bg-gray-300/50 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex flex-col gap-2 sm:gap-4 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 p-2 sm:p-3 bg-white rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    htmlFor: \"dashboard-toggle\",\n                                    className: \"text-xs sm:text-sm font-medium\",\n                                    children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    id: \"dashboard-toggle\",\n                                    checked: shouldShowMainDash,\n                                    onCheckedChange: handleToggleDashboard\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 max-h-full overflow-hidden\",\n                        children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                            loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            ltvChartData: dashboardData.ltvChartData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 max-h-full overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-full p-2 sm:p-4 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                defaultValue: \"updates\",\n                                className: \"h-full flex flex-col overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                        className: \"grid w-full grid-cols-3 mb-2 sm:mb-4 bg-gray-100 p-1 rounded-full h-10 sm:h-12 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"updates\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"account\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"transactions\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-0 max-h-full overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"updates\",\n                                                className: \"h-full overflow-y-auto space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 sm:space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            trends: dashboardData.topTrendsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            coins: dashboardData.newCoinsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"account\",\n                                                className: \"h-full overflow-y-auto space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"transactions\",\n                                                className: \"h-full overflow-y-auto space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    // Desktop Layout (≥ 1200px)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen p-4 lg:p-6 bg-gray-300/50 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[6fr_4fr] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 max-h-full flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 mb-4 lg:mb-6 p-3 lg:p-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"dashboard-toggle-desktop\",\n                                        className: \"text-sm lg:text-base font-medium\",\n                                        children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                        id: \"dashboard-toggle-desktop\",\n                                        checked: shouldShowMainDash,\n                                        onCheckedChange: handleToggleDashboard\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0 max-h-full overflow-hidden\",\n                            children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                                loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                ltvChartData: dashboardData.ltvChartData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 max-h-full w-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"h-full w-full p-3 lg:p-4 xl:p-6 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            defaultValue: \"updates\",\n                            className: \"h-full flex flex-col bg-transparent overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid w-full grid-cols-3 mb-4 lg:mb-6 p-1 bg-transparent rounded-full h-10 lg:h-12 flex-shrink-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"updates\",\n                                            className: \"data-[state=active]:bg-[#466DFF] data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm hover:cursor-pointer\",\n                                            children: \"Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"account\",\n                                            className: \"data-[state=active]:bg-[#466DFF] data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm\",\n                                            children: \"Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"transactions\",\n                                            className: \"data-[state=active]:bg-[#466DFF] data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm\",\n                                            children: \"Transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-h-0 max-h-full overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"updates\",\n                                            className: \"h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 lg:space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        trends: dashboardData.topTrendsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        coins: dashboardData.newCoinsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"account\",\n                                            className: \"h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"transactions\",\n                                            className: \"h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardHomePage, \"AjWREQYOBB9rwT0eFkHbJXzz0/o=\", false, function() {\n    return [\n        _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType\n    ];\n});\n_c = DashboardHomePage;\nvar _c;\n$RefreshReg$(_c, \"DashboardHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});