"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TransactionItem.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/TransactionItem.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransactionItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _general_CryptoIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../general/CryptoIcon */ \"(app-pages-browser)/./src/components/general/CryptoIcon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction TransactionItem(param) {\n    let { type, description, amount, date, cryptoSymbol, icon, iconColor, iconBg } = param;\n    // Function to extract crypto symbol from description or amount\n    const getCryptoSymbolFromTransaction = ()=>{\n        if (cryptoSymbol) return cryptoSymbol;\n        // Extract from amount (e.g., \"0.0500 BTC\" -> \"BTC\")\n        const amountMatch = amount.match(/([A-Z]{3,4})$/);\n        if (amountMatch) return amountMatch[1];\n        // Extract from description (e.g., \"Buy BTC\" -> \"BTC\")\n        const descMatch = description.match(/\\b(BTC|ETH|USDT|ADA|DASH|SOL|XMR|DOT)\\b/i);\n        if (descMatch) return descMatch[1].toUpperCase();\n        return null;\n    };\n    const detectedCryptoSymbol = getCryptoSymbolFromTransaction();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between py-3 border-b border-border/30 last:border-b-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    detectedCryptoSymbol ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_general_CryptoIcon__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        symbol: detectedCryptoSymbol,\n                        size: 20,\n                        className: \"w-8 h-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this) : // Fallback to original icon for non-crypto transactions\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold\",\n                        style: {\n                            backgroundColor: iconBg,\n                            color: iconColor\n                        },\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium text-foreground\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: date\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 20\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-right\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-semibold text-foreground\",\n                    children: amount\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\TransactionItem.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c = TransactionItem;\nvar _c;\n$RefreshReg$(_c, \"TransactionItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TransactionItem.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/WalletItem.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/WalletItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _general_CryptoIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../general/CryptoIcon */ \"(app-pages-browser)/./src/components/general/CryptoIcon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction WalletItem(param) {\n    let { name, symbol, icon, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_general_CryptoIcon__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        symbol: symbol,\n                        size: 20,\n                        className: \"w-8 h-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\WalletItem.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium text-foreground\",\n                        children: name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\WalletItem.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\WalletItem.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-4 w-4 text-muted-foreground\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\WalletItem.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\WalletItem.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_c = WalletItem;\nvar _c;\n$RefreshReg$(_c, \"WalletItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/WalletItem.tsx\n"));

/***/ })

});