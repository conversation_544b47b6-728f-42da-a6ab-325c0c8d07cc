"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LoanData } from '../../types/schema';
import { formatCryptoAmount, formatUSDAmount } from '../../utils/formatters';
import { CryptoCurrency } from '../../types/enums';
import CryptoIcon from '../general/CryptoIcon';

interface LoanCardProps {
  loan: LoanData;
}


const getHealthColor = (health: string) => {
  switch (health.toLowerCase()) {
    case 'green':
      return 'bg-green-500';
    case 'yellow':
      return 'bg-yellow-500';
    case 'red':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

export const LoanCard: React.FC<LoanCardProps> = ({ loan }) => {
  return (
    <Card className={`p-6 relative transition-all duration-200 hover:shadow-lg max ${
      loan.isHighlighted ? 'ring-2 ring-red-200 bg-red-50/30' : ''
    }`}>
      {/* Health indicator dot */}
      <div className={`absolute top-4 right-4 w-3 h-3 rounded-full ${getHealthColor(loan.health)}`} />
      
      <div className="flex items-start gap-4">
        <CryptoIcon 
          symbol={loan.collateralCurrency}
          size={24}
          className="w-8 h-8"
        />
        
        <div className="flex-1 space-y-3">
          <div>
            <div className="flex items-center gap-2 mb-1">
              <span className="text-lg font-semibold">
                {formatCryptoAmount(loan.collateralAmount, loan.collateralCurrency)}
              </span>
            </div>
            <div className="text-sm text-muted-foreground">
              {loan.collateralValueUSD} USDT
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-muted-foreground mb-1">Current rate</div>
              <div className="font-medium">
                {formatUSDAmount(loan.currentRate)}
              </div>
              <div className="text-xs text-muted-foreground">
                {loan.ratePair}
              </div>
            </div>
            
            <div>
              <div className="text-muted-foreground mb-1">Margin call</div>
              <div className="font-medium">
                {formatUSDAmount(loan.marginCall)}
              </div>
              <div className="text-xs text-muted-foreground">
                {loan.ratePair}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};