"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/LoanCard.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/LoanCard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoanCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _utils_statusStyles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/statusStyles */ \"(app-pages-browser)/./src/utils/statusStyles.ts\");\n/* harmony import */ var _general_CryptoIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../general/CryptoIcon */ \"(app-pages-browser)/./src/components/general/CryptoIcon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LoanCard(param) {\n    let { id, tokenTicker, loanAmount = 0, loanValueUSDT = 0, status, marginCallAmount = 0 } = param;\n    const statusVariant = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_2__.getStatusFromLoanHealth)(status);\n    const statusStyles = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_2__.getStatusStyles)(statusVariant);\n    // Add safety checks for numeric values\n    const safeLoanAmount = typeof loanAmount === 'number' && !isNaN(loanAmount) ? loanAmount : 0;\n    const safeLoanValueUSDT = typeof loanValueUSDT === 'number' && !isNaN(loanValueUSDT) ? loanValueUSDT : 0;\n    const safeMarginCallAmount = typeof marginCallAmount === 'number' && !isNaN(marginCallAmount) ? marginCallAmount : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"relative overflow-hidden rounded-4xl border-2 shadow-sm h-full min-h-0 p-3\",\n        style: {\n            backgroundColor: statusStyles.cardBG,\n            borderColor: statusStyles.cardBorderColour\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n            className: \"h-full flex flex-col justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_general_CryptoIcon__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    symbol: tokenTicker,\n                                    size: 24,\n                                    className: \"p-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"sm:text-xs md:text-base lg:text-xl xl:text-2xl font-semibold\",\n                                            children: [\n                                                safeLoanAmount,\n                                                \" \",\n                                                tokenTicker\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"$\",\n                                                safeLoanValueUSDT.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center rounded-full p-1 border\",\n                            style: {\n                                borderColor: statusStyles.statusBorderColour\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 md:w-3 md:h-3 rounded-full\",\n                                style: {\n                                    backgroundColor: statusStyles.statusColour\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-3 mt-3 md:mt-4 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground capitalize\",\n                            children: \"Margin call \"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"sm:text-xs md:text-sm lg:text-xl xl:text-xl font-semibold tracking-tight\",\n                            children: [\n                                \"$\",\n                                safeMarginCallAmount.toLocaleString(),\n                                \" \",\n                                tokenTicker,\n                                \"/USDT\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c = LoanCard;\nvar _c;\n$RefreshReg$(_c, \"LoanCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/LoanCard.tsx\n"));

/***/ })

});