"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/general/CryptoIcon.tsx":
/*!***********************************************!*\
  !*** ./src/components/general/CryptoIcon.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CryptoIcon),\n/* harmony export */   getCryptoIcon: () => (/* binding */ getCryptoIcon),\n/* harmony export */   getCryptoIconColor: () => (/* binding */ getCryptoIconColor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @thirdweb-dev/chain-icons */ \"(app-pages-browser)/./node_modules/@thirdweb-dev/chain-icons/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default,getCryptoIcon,getCryptoIconColor auto */ \n\n// Extended mapping for more cryptocurrencies beyond the enum\nconst getCryptoIcon = (symbol)=>{\n    const normalizedSymbol = symbol.toLowerCase();\n    switch(normalizedSymbol){\n        case 'btc':\n        case 'bitcoin':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Bitcoin;\n        case 'eth':\n        case 'ethereum':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Ethereum;\n        case 'usdt':\n        case 'tether':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Tether;\n        case 'ada':\n        case 'cardano':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Cardano;\n        case 'dash':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Dash;\n        // For cryptocurrencies not available in thirdweb icons, we'll use Bitcoin as fallback\n        case 'sol':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Solana;\n        case 'solana':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Solana;\n        case 'xmr':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Monero;\n        case 'monero':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Monero;\n        case 'dot':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.PolkadotNew;\n        case 'polkadot':\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.PolkadotNew;\n        default:\n            return _thirdweb_dev_chain_icons__WEBPACK_IMPORTED_MODULE_1__.Bitcoin;\n    }\n};\nconst getCryptoIconColor = (symbol)=>{\n    const normalizedSymbol = symbol.toLowerCase();\n    switch(normalizedSymbol){\n        case 'btc':\n        case 'bitcoin':\n            return \"#F7931A\"; // Bitcoin orange\n        case 'eth':\n        case 'ethereum':\n            return \"#DCDCDC\"; // Ethereum blue\n        case 'usdt':\n        case 'tether':\n            return \"#26A17B\"; // Tether green\n        case 'ada':\n        case 'cardano':\n            return \"#FFFFFF\"; // Cardano blue\n            return \"#FFFFFF\"; // Cardano blue\n        case 'dash':\n            return \"#FFFFFF\"; // Dash blue\n        case 'sol':\n            return \"#0b0314\"; // Solana purple\n        case 'solana':\n            return \"#0b0314\"; // Solana purple\n        case 'xmr':\n        case 'monero':\n            return \"#FF6600\"; // Monero orange\n        case 'dot':\n        case 'polkadot':\n            return \"#FFFFFF\"; // Polkadot pink\n        default:\n            return \"#F7931A\"; // Default to Bitcoin orange\n    }\n};\nfunction CryptoIcon(param) {\n    let { symbol, size = 24, className = \"\" } = param;\n    const IconComponent = getCryptoIcon(symbol);\n    const iconColor = getCryptoIconColor(symbol);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-full flex items-center justify-center \".concat(className),\n        style: {\n            backgroundColor: iconColor\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n            width: size,\n            height: size,\n            fill: \"white\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\CryptoIcon.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\CryptoIcon.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_c = CryptoIcon;\n// Export the utility functions for use in other components\n\nvar _c;\n$RefreshReg$(_c, \"CryptoIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/general/CryptoIcon.tsx\n"));

/***/ })

});