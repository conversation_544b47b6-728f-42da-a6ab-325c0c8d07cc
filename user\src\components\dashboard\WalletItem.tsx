"use client";

import { ChevronRight } from "lucide-react";
import CryptoIcon from "../general/CryptoIcon";

interface WalletItemProps {
  name: string;
  symbol: string;
  icon?: string; // Keep for backward compatibility
  color?: string; // Keep for backward compatibility
}

export default function WalletItem({ name, symbol, icon, color }: WalletItemProps) {
  return (
    <div className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer">
      <div className="flex items-center gap-3">
        <CryptoIcon 
          symbol={symbol}
          size={20}
          className="w-8 h-8"
        />
        <span className="font-medium text-foreground">{name}</span>
      </div>
      <ChevronRight className="h-4 w-4 text-muted-foreground" />
    </div>
  );
}