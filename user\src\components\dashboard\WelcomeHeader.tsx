"use client";

import { useState } from "react";
import { Bell } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import NotificationPanel from "./NotificationPanel";

export default function WelcomeHeader() {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  const toggleNotifications = () => {
    setIsNotificationOpen(!isNotificationOpen);
  };

  const closeNotifications = () => {
    setIsNotificationOpen(false);
  };

  return (
    <div className="flex items-start justify-between mb-8">
      <div className="flex-1">
        <h1 className="text-3xl font-bold text-foreground mb-4">
          Hello and Welcome!
        </h1>
        <p className="text-muted-foreground text-lg leading-relaxed max-w-2xl">
          Dive into the world of crypto lending—secure, simple, and designed for beginners. 
          Start using your digital assets to unlock new earning opportunities today!
        </p>
      </div>
      <div className="relative ml-6">
        <Button 
          variant="ghost" 
          size="icon" 
          className="rounded-full border border-border"
          onClick={toggleNotifications}
        >
          <Bell className="h-5 w-5" />
        </Button>
        <NotificationPanel 
          isOpen={isNotificationOpen} 
          onClose={closeNotifications}
        />
      </div>
    </div>
  );
}