"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/NewListedCoins.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/NewListedCoins.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewListedCoins)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _general_CryptoIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../general/CryptoIcon */ \"(app-pages-browser)/./src/components/general/CryptoIcon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NewListedCoins(param) {\n    let { coins } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-3\",\n                children: \"New listed coins\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-muted-foreground mb-4\",\n                children: \"The capabilities of LendBloc Wallet have been enhanced to accommodate the storage and exchange of newly supported assets.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-3\",\n                children: coins.map((coin)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 p-3 rounded-lg border border-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_general_CryptoIcon__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                symbol: coin.symbol,\n                                size: 20,\n                                className: \"w-8 h-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-sm\",\n                                        children: coin.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"(\",\n                                            coin.symbol,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, coin.symbol, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\NewListedCoins.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = NewListedCoins;\nvar _c;\n$RefreshReg$(_c, \"NewListedCoins\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/NewListedCoins.tsx\n"));

/***/ })

});