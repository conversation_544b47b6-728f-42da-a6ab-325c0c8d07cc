"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/AccountBalance.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/AccountBalance.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AccountBalance)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction BalanceItem(param) {\n    let { label, value, hasInfo = false, hasArrow = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"w-full rounded-3xl p-4 bg-[#F8F8F8]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        hasInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"h-4 w-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 23\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-foreground\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        hasArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 24\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = BalanceItem;\nfunction AccountBalance() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BalanceItem, {\n                label: \"USDT Balance\",\n                value: \"21,558.00\",\n                hasArrow: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BalanceItem, {\n                label: \"Savings\",\n                value: \"$4,253.50\",\n                hasArrow: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BalanceItem, {\n                label: \"Collateral balance\",\n                value: \"$45,669.12\",\n                hasInfo: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BalanceItem, {\n                label: \"Assets value\",\n                value: \"$7,899.45\",\n                hasInfo: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\AccountBalance.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AccountBalance;\nvar _c, _c1;\n$RefreshReg$(_c, \"BalanceItem\");\n$RefreshReg$(_c1, \"AccountBalance\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/AccountBalance.tsx\n"));

/***/ })

});